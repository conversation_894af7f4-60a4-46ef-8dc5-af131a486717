// ReSharper disable VirtualMemberNeverOverridden.Global
// ReSharper disable UnusedMember.Global

// ReSharper disable UnusedMemberHierarchy.Global
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global
// ReSharper disable UnusedParameter.Global
namespace ModuleSystem
{
    public abstract class Module
    {
        public ModularEntity OwningEntity { get; private set; }
        
        public void Added(ModularEntity entity)
        {
            OwningEntity  = entity;
        }

        public void Removed(ModularEntity entity)
        {
            OwningEntity = null;
        }
        
        protected virtual void OnAdded(ModularEntity entity) {}
        protected virtual void OnRemoved(ModularEntity entity) {}
    }
}
