using PurrNet;
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedMember.Global

namespace ModuleSystem
{
    public class ModularEntity : NetworkBehaviour
    {
        public SyncHashSet<Module> Modules { get; } = new();

        public void AddModule(Module module)
        {
            Modules.Add(module);
            module.Added(this);
        }

        public void RemoveModule(Module module)
        {
            Modules.Remove(module);
            module.Removed(this);
        }
    }
}
